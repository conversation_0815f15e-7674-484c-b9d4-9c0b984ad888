<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>cn.genn.boot</groupId>
    <artifactId>genn-spring-boot-parent</artifactId>
    <version>1.0.0-RELEASE</version>
  </parent>
  <groupId>cn.genn.app</groupId>
  <artifactId>genn-orch-tfs</artifactId>
  <version>1.0.0-RELEASE</version>
  <packaging>pom</packaging>
  <description>工时填报系统</description>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <modules>
    <module>genn-orch-tfs-api</module>
    <module>genn-orch-tfs-server</module>
  </modules>
  <properties>
    <skipTests>true</skipTests>
  </properties>
  <dependencies>
    <dependency>
      <groupId>io.swagger</groupId>
      <artifactId>swagger-annotations</artifactId>
    </dependency>
  </dependencies>
</project>
