<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>cn.genn.app</groupId>
    <artifactId>genn-orch-tfs</artifactId>
    <version>1.0.0-RELEASE</version>
  </parent>
  <groupId>cn.genn.app</groupId>
  <artifactId>genn-orch-tfs-server</artifactId>
  <version>1.0.0-RELEASE</version>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <properties>
    <easyexcel.version>3.3.3</easyexcel.version>
    <esdk-obs-java.version>3.22.3.1</esdk-obs-java.version>
    <aws-java-sdk-s3.version>1.12.429</aws-java-sdk-s3.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
  </properties>
  <dependencies>
    <dependency>
      <groupId>cn.genn.boot</groupId>
      <artifactId>genn-spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.genn.boot</groupId>
      <artifactId>genn-spring-boot-starter-swagger</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.genn.boot</groupId>
      <artifactId>genn-spring-boot-starter-event-spring</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.genn.boot</groupId>
      <artifactId>genn-spring-boot-starter-monitor</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.genn.app</groupId>
      <artifactId>genn-orch-tfs-api</artifactId>
      <version>${genn-orch-tfs-api.version}</version>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>com.squareup.okhttp3</groupId>
      <artifactId>okhttp</artifactId>
      <version>4.8.1</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-configuration-processor</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>com.larksuite.oapi</groupId>
      <artifactId>oapi-sdk</artifactId>
      <version>2.4.12</version>
      <optional>true</optional>
    </dependency>
  </dependencies>
  <build>
    <finalName>genn-orch-tfs</finalName>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </build>
  <profiles>
    <profile>
      <id>local</id>
      <build>
        <resources>
          <resource>
            <filtering>true</filtering>
            <directory>/Users/<USER>/IdeaProjects/workspace/trans/genn-orch-tfs/genn-orch-tfs-server/src/main/resources</directory>
            <excludes>
              <exclude>*-dev.yml</exclude>
              <exclude>*-test.yml</exclude>
              <exclude>*-pro.yml</exclude>
            </excludes>
          </resource>
        </resources>
      </build>
      <properties>
        <genn-orch-tfs-api.version>1.0.0-SNAPSHOT</genn-orch-tfs-api.version>
        <spring.profiles.active>local</spring.profiles.active>
      </properties>
    </profile>
    <profile>
      <id>dev</id>
      <build>
        <resources>
          <resource>
            <filtering>true</filtering>
            <directory>src/main/resources</directory>
            <excludes>
              <exclude>*-local.yml</exclude>
              <exclude>*-test.yml</exclude>
              <exclude>*-pro.yml</exclude>
            </excludes>
          </resource>
        </resources>
      </build>
      <properties>
        <genn-orch-tfs-api.version>${genn-service-api.version}</genn-orch-tfs-api.version>
        <spring.profiles.active>dev</spring.profiles.active>
      </properties>
    </profile>
    <profile>
      <id>test</id>
      <build>
        <resources>
          <resource>
            <filtering>true</filtering>
            <directory>src/main/resources</directory>
            <excludes>
              <exclude>*-dev.yml</exclude>
              <exclude>*-local.yml</exclude>
              <exclude>*-pro.yml</exclude>
            </excludes>
          </resource>
        </resources>
      </build>
      <properties>
        <genn-orch-tfs-api.version>${genn-service-api.version}</genn-orch-tfs-api.version>
        <spring.profiles.active>test</spring.profiles.active>
      </properties>
    </profile>
    <profile>
      <id>pro</id>
      <build>
        <resources>
          <resource>
            <filtering>true</filtering>
            <directory>src/main/resources</directory>
            <excludes>
              <exclude>*-dev.yml</exclude>
              <exclude>*-test.yml</exclude>
              <exclude>*-local.yml</exclude>
            </excludes>
          </resource>
        </resources>
      </build>
      <properties>
        <genn-orch-tfs-api.version>1.0.2-RELEASE</genn-orch-tfs-api.version>
        <spring.profiles.active>pro</spring.profiles.active>
      </properties>
    </profile>
  </profiles>
</project>
