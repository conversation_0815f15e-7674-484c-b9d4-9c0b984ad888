package cn.genn.orch.tfs.application.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class DropDataDTO {

    @ApiModelProperty("下拉框显示名称")
    private String name;

    @ApiModelProperty("下拉框关联")
    private String code;

    @ApiModelProperty("关联下拉框")
    private List<DropDataDTO> children = new ArrayList<>();

}
