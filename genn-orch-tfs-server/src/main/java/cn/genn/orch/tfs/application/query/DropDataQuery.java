package cn.genn.orch.tfs.application.query;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@Data
public class DropDataQuery {

    @ApiModelProperty("飞书用户OpenId")
    @NotBlank(message = "openId不能为空")
    private String openId;

    @ApiModelProperty("填报时间范围-开始时间")
    @NotNull(message = "填报时间范围不能为空")
    private LocalDate startTime;

    @ApiModelProperty("填报时间范围-结束时间")
    @NotNull(message = "填报时间范围不能为空")
    private LocalDate endTime;
}
