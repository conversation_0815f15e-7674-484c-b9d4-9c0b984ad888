package cn.genn.orch.tfs.application.client;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.orch.tfs.application.dto.FsTextData;
import cn.genn.orch.tfs.application.dto.FsUser;
import cn.hutool.core.util.ObjUtil;
import com.lark.oapi.service.attendance.v1.model.UserLeave;
import com.lark.oapi.service.attendance.v1.model.UserOvertimeWork;
import com.lark.oapi.service.bitable.v1.model.Condition;
import com.lark.oapi.service.bitable.v1.model.FilterInfo;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Slf4j
public class FeishuAppHelper {

    private static final DateTimeFormatter DATE_TIME_DATE = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public static String getText(Object project) {
        if (project == null) {
            return null;
//            throw new BusinessException("字段识别失败!");
        }
        List<FsTextData> fsTextData = JsonUtils.parseToList(JsonUtils.toJson(project), FsTextData.class);
        return fsTextData.get(0).getText();
    }

    public static String getDrop(Object project){
        if (project == null) {
            return null;
//            throw new BusinessException("字段识别失败!!");
        }
        return project.toString();
    }

    public static List<FsUser> getUser(Object project) {
        if (project == null) {
            return null;
        }
        String json = JsonUtils.toJson(project);
        return JsonUtils.parseToList(json, FsUser.class);
    }

    public static Double getDouble(Object project) {
        if (project == null) {
            return 0.0;
//            throw new BusinessException("字段识别失败!!!");
        }
        if (project instanceof Double) {
            return (Double) project;
        }
        return 0.0;
    }

    /**
     * 处理请假数据
     * @param leaves
     * @return
     */
    public static int getEffectiveLeaveHours(UserLeave[] leaves) {
        if (ObjUtil.isNull(leaves)) return 0;

        int totalEffectiveMinutes = 0;

        for (UserLeave leaf : leaves) {
            LocalDateTime start = LocalDateTime.parse(leaf.getStartTime(), DATE_TIME_DATE);
            LocalDateTime end = LocalDateTime.parse(leaf.getEndTime(), DATE_TIME_DATE);

            // 按天遍历请假时间范围
            LocalDate currentDate = start.toLocalDate();
            LocalDate endDate = end.toLocalDate();

            while (!currentDate.isAfter(endDate)) {
                // 定义当天的工作时间段
                LocalDateTime workStartAM = LocalDateTime.of(currentDate, LocalTime.of(9, 0));
                LocalDateTime workEndAM = LocalDateTime.of(currentDate, LocalTime.of(12, 0));
                LocalDateTime workStartPM = LocalDateTime.of(currentDate, LocalTime.of(13, 0));
                LocalDateTime workEndPM = LocalDateTime.of(currentDate, LocalTime.of(18, 0));
                // 计算与上午工作时间段的交集
                totalEffectiveMinutes += FeishuAppHelper.calculateOverlapMinutes(start, end, workStartAM, workEndAM);
                // 计算与下午工作时间段的交集
                totalEffectiveMinutes += FeishuAppHelper.calculateOverlapMinutes(start, end, workStartPM, workEndPM);
                // 下一天
                currentDate = currentDate.plusDays(1);
            }
        }
        // 转换为小时，向上取整或保留分钟
        return Math.round((float) totalEffectiveMinutes / 60);
    }

    // 计算两个时间段的交集分钟数
    public static int calculateOverlapMinutes(LocalDateTime start1, LocalDateTime end1,
                                              LocalDateTime start2, LocalDateTime end2) {
        LocalDateTime overlapStart = start1.isAfter(start2) ? start1 : start2;
        LocalDateTime overlapEnd = end1.isBefore(end2) ? end1 : end2;
        if (overlapStart.isAfter(overlapEnd) || overlapStart.isEqual(overlapEnd)) {
            return 0;
        }

        return (int) Duration.between(overlapStart, overlapEnd).toMinutes();
    }

    /**
     * 处理加班数据
     * @param overtimeWorks
     * @return
     */
    public static int getOvertimeHour(UserOvertimeWork[] overtimeWorks) {
        int hour = 0;
        if (ObjUtil.isNull(overtimeWorks)) return hour;
        for (UserOvertimeWork time : overtimeWorks) {
            Double duration = time.getDuration();
            switch (time.getUnit()) {
                case 1:
                    hour = (int) (duration * 8);
                    break;
                case 2:
                    hour = duration.intValue();
                    break;
                case 3:
                    hour = (int) (duration * 4);
                    break;
                case 4:
                    hour = (int) (duration / 2);
                    break;
            }
        }
        return hour;
    }

    public static FilterInfo getQueryWikFilter() {
        return FilterInfo.newBuilder()
                .conjunction("and")
                .conditions(new Condition[]{
                        Condition.newBuilder()
                                .fieldName("上线状态")
                                .operator("contains")
                                .value(new String[]{"研发中","验收中","已上线","策划中"})
                                .build()
                })
//                .children()
                .build();
    }

    public static FilterInfo getSaveWikFilter(String time) {
        return FilterInfo.newBuilder()
                .conjunction("and")
                .conditions(new Condition[]{
                        Condition.newBuilder()
                                .fieldName("对应周")
                                .operator("is")
                                .value(new String[]{time})
                                .build()
                })
                .build();
    }
}
