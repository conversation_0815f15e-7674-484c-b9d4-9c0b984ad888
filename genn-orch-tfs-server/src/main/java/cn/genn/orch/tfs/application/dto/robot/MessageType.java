package cn.genn.orch.tfs.application.dto.robot;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum MessageType {

    /**
     * 文本
     */
    TEXT("text", "文本"),
    /**
     * 富文本
     */
    POST("post", "富文本"),
    /**
     * 群名片
     */
    SHARE_CHAT("share_chat", "群名片"),
    /**
     * 图片
     */
    IMAGE("image", "图片"),
    /**
     * 消息卡片
     */
    INTERACTIVE("interactive", "消息卡片"),
    ;

    @JsonValue
    @EnumValue
    private final String type;
    private final String desc;

    MessageType(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static MessageType of(String type) {
        for (MessageType value : MessageType.values()) {
            if (value.type.equals(type)) {
                return value;
            }
        }
        return null;
    }


}
