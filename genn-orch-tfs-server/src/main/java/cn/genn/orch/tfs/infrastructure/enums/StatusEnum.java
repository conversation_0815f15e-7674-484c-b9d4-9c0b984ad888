package cn.genn.orch.tfs.infrastructure.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum StatusEnum {

    PROJECT(0, "未填报"),
    DEMAND(1, "已填报"),

    ;

    @EnumValue
    @JsonValue
    private final Integer code;

    private final String description;

    private static final Map<Integer, StatusEnum> VALUES = new HashMap<>();

    static {
        for (final StatusEnum item : StatusEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static StatusEnum of(Integer code) {
        return VALUES.get(code);
    }
}
