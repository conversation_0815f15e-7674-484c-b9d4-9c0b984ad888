package cn.genn.orch.tfs.application.service;

import cn.genn.orch.tfs.application.client.FeishuAppClient;
import cn.genn.orch.tfs.application.client.FeishuAppHelper;
import cn.genn.orch.tfs.application.dto.FsUser;
import cn.genn.orch.tfs.application.dto.UserNotifyDTO;
import cn.genn.orch.tfs.infrastructure.properties.TfsProperties;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.lark.oapi.service.bitable.v1.model.AppTableRecord;
import com.lark.oapi.service.bitable.v1.model.FilterInfo;
import com.lark.oapi.service.contact.v3.model.User;
import com.lark.oapi.service.contact.v3.model.UserContactInfo;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FsNotifyService {

    @Resource
    private FeishuAppClient feishuAppClient;
    @Resource
    private TfsProperties tfsProperties;

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter DATE_TIME_FORMATTER2 = DateTimeFormatter.ofPattern("M月d日");

    /**
     * 指定人员通知
     *
     * @param userNotifyDTO
     */
    public void sendNotifyByTool(UserNotifyDTO userNotifyDTO) {
        LocalDate day = LocalDate.now();
        if (ObjUtil.isNotNull(userNotifyDTO.getFillTime())) {
            day = userNotifyDTO.getFillTime();
        }
        List<UserContactInfo> openIdByTelephone = feishuAppClient.getOpenIdByTelephone(userNotifyDTO.getTelephones());
        List<FsUser> users = openIdByTelephone.stream().map(userInfo -> {
            FsUser fsUser = new FsUser();
            fsUser.setOpenId(userInfo.getUserId());
            return fsUser;
        }).collect(Collectors.toList());
        this.sendNotify(users, day);
    }

    /**
     * 多维表格获取通知用户,并发送数据
     */
    public void sendNotifyByWik(LocalDate day) {
        //1.获取待通知用户;
        //获取数据
        String wikToken = tfsProperties.getWik().getQueryWikToken();
        String wikTableId = tfsProperties.getWik().getQueryWikTableId();
        FilterInfo wikFilter = FeishuAppHelper.getQueryWikFilter();
        List<AppTableRecord> wikRecordList = feishuAppClient.getWikRecordList(wikToken, wikTableId, wikFilter);
        //处理数据
        List<FsUser> users = new ArrayList<>();
        for (AppTableRecord appTableRecord : wikRecordList) {
            Map<String, Object> recordMap = appTableRecord.getFields();
            List<FsUser> openIds = FeishuAppHelper.getUser(recordMap.get("工时填报人员"));
            if (CollUtil.isNotEmpty(openIds)) {
                users.addAll(openIds);
            }
        }
        this.sendNotify(users, day);
    }

    /**
     * 所有权限范围内的人员都发送通知
     */
    public void sendNotifyByAll(LocalDate day) {
        List<String> departmentAll = feishuAppClient.getDepartmentAll();
        List<User> departmentUserList = feishuAppClient.getDepartmentUserList(departmentAll);
        List<FsUser> users = departmentUserList.stream().map(userInfo -> {
            FsUser fsUser = new FsUser();
            fsUser.setOpenId(userInfo.getOpenId());
            return fsUser;
        }).distinct().collect(Collectors.toList());
//        log.info("users:{},size:{}", JsonUtils.toJson( users), users.size());
        this.sendNotify(users, day);
    }


    @SneakyThrows
    public void sendNotify(List<FsUser> users, LocalDate day) {
        if (CollUtil.isEmpty(users)) {
            return;
        }
        //2.获取人员本周工时
        LocalDate lastDay = day.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));
        LocalDate firstDay = lastDay.minusDays(6);
        users = users.stream().distinct().collect(Collectors.toList());
        String cardTemplateId = tfsProperties.getCardSend().getTemplateId();
        String pcUrl = tfsProperties.getCardSend().getPcUrl() + "?startTime=" + firstDay.format(DATE_TIME_FORMATTER) + "&endTime=" + lastDay.format(DATE_TIME_FORMATTER);
        String appUrl = tfsProperties.getCardSend().getAppUrl() + "?startTime=" + firstDay.format(DATE_TIME_FORMATTER) + "&endTime=" + lastDay.format(DATE_TIME_FORMATTER);
        String timeRange = firstDay.format(DATE_TIME_FORMATTER2) + "-" + lastDay.format(DATE_TIME_FORMATTER2);
        List<List<FsUser>> partition = Lists.partition(users, 10);
        for (List<FsUser> fsUsers : partition) {
            Map<String, Integer> workHourMap = feishuAppClient.getOverTimeCache(fsUsers.stream().map(FsUser::getOpenId).collect(Collectors.toList()), firstDay, lastDay);
            //3.发送卡片通知
            for (String key : workHourMap.keySet()) {
                if (workHourMap.get(key) > 0) {
                    String openPcUrl = pcUrl + "&openId=" + key;
                    openPcUrl = tfsProperties.getCardSend().getPcBaseUrl() + URLEncoder.encode(openPcUrl, "UTF-8");
                    String openAppUrl = appUrl + "&openId=" + key;
                    //卡片通知;
                    this.sendNotifyCard(key, timeRange, openPcUrl, openAppUrl, cardTemplateId);
                }
            }
        }
    }


    /**
     * 通知卡片
     */
    public void sendNotifyCard(String openId, String timeRange, String pcUrl, String appUrl, String templateId) {
        Map<String, Object> templateVariable = new HashMap<>();
        templateVariable.put("timeRange", timeRange);
        templateVariable.put("pcUrl", pcUrl);
        templateVariable.put("appUrl", appUrl);
        feishuAppClient.sendCard(openId, templateId, templateVariable);
    }
}
