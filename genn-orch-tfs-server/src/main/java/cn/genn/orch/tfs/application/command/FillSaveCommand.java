package cn.genn.orch.tfs.application.command;

import cn.genn.orch.tfs.application.dto.FillValueDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

@Data
public class FillSaveCommand {

    @ApiModelProperty("飞书用户OpenId")
    @NotBlank(message = "openId不能为空")
    private String openId;

    @ApiModelProperty("填报时间范围-开始时间")
    @NotNull(message = "填报时间范围不能为空")
    private LocalDate startTime;

    @ApiModelProperty("填报时间范围-结束时间")
    @NotNull(message = "填报时间范围不能为空")
    private LocalDate endTime;

    @Valid
    @ApiModelProperty("填报内容")
    @NotEmpty(message = "填报内容不能为空")
    private List<FillValueDTO> fillValues;

}
