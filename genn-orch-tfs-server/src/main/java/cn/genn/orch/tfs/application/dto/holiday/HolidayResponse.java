package cn.genn.orch.tfs.application.dto.holiday;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@NoArgsConstructor
@Data
public class HolidayResponse {


    @JsonProperty("code")
    private Integer code;
    @JsonProperty("holiday")
    private Map<String, HolidayDTO> holiday;

    public List<HolidayDTO> getHolidayList() {
        return new ArrayList<>(holiday.values());
    }

}
