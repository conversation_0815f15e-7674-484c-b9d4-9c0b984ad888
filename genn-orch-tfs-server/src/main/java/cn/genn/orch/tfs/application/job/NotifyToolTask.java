package cn.genn.orch.tfs.application.job;

import cn.genn.core.exception.BusinessException;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.job.xxl.component.AbstractJobHandler;
import cn.genn.orch.tfs.application.dto.UserNotifyDTO;
import cn.genn.orch.tfs.application.service.FsNotifyService;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 指定用户发送通知
 */
@Slf4j
@Component
public class NotifyToolTask extends AbstractJobHandler {

    @Resource
    private FsNotifyService fsNotifyService;

    @Override
    public void doExecute() {
        log.info("指定用户发送通知 开始执行");
        long start = System.currentTimeMillis();
        String jobParam = XxlJobHelper.getJobParam();
        if(StrUtil.isEmpty(jobParam)){
            throw new BusinessException("缺少入参,格式:{\"fillTime\": \"YYYY-MM-dd\", \"telephones\": []}");
        }
        UserNotifyDTO parse = JsonUtils.parse(jobParam, UserNotifyDTO.class);
        if(ObjUtil.isNull(parse) || CollUtil.isEmpty(parse.getTelephones())){
            throw new BusinessException("入参格式不正确,格式:{\"fillTime\": \"YYYY-MM-dd\", \"telephones\": []}");
        }
        fsNotifyService.sendNotifyByTool(parse);
        log.info("指定用户发送通知,执行耗时:{}s", (System.currentTimeMillis() - start) / 1000);
    }
}
