package cn.genn.orch.tfs.application.service;

import cn.genn.core.exception.BusinessException;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.orch.tfs.application.client.FeishuAppClient;
import cn.genn.orch.tfs.application.client.FeishuAppHelper;
import cn.genn.orch.tfs.application.command.FillSaveDayCommand;
import cn.genn.orch.tfs.application.dto.day.FIllDayDTO;
import cn.genn.orch.tfs.application.dto.day.FillDayValueDTO;
import cn.genn.orch.tfs.application.query.DropDayQuery;
import cn.genn.orch.tfs.infrastructure.constant.CacheConstants;
import cn.genn.orch.tfs.infrastructure.enums.StatusEnum;
import cn.genn.orch.tfs.infrastructure.exception.MessageCode;
import cn.genn.orch.tfs.infrastructure.mapper.TfsDayFillLogMapper;
import cn.genn.orch.tfs.infrastructure.mapper.TfsDayFillMapper;
import cn.genn.orch.tfs.infrastructure.po.TfsDayFillLogPO;
import cn.genn.orch.tfs.infrastructure.po.TfsDayFillPO;
import cn.genn.orch.tfs.infrastructure.properties.TfsProperties;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.lark.oapi.service.bitable.v1.model.AppTableRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FsDayFillService {

    @Resource
    private FeishuAppClient feishuAppClient;
    @Resource
    private TfsProperties tfsProperties;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private TfsDayFillMapper tfsDayFillMapper;
    @Resource
    private TfsDayFillLogMapper tfsDayFillLogMapper;

    public FIllDayDTO queryDayFill(DropDayQuery query) {
        FIllDayDTO fillDTO = new FIllDayDTO(query);
        CompletableFuture<Integer> weekTimeFuture = CompletableFuture.supplyAsync(() -> feishuAppClient.getOverTimeCache(Collections.singletonList(query.getOpenId()), query.getDay(), query.getDay()).get(query.getOpenId()))
                .exceptionally(ex -> {
                    log.error("获取日总工时异常", ex);
                    return 0;
                });
        CompletableFuture<List<String>> dropDataListFuture = CompletableFuture.supplyAsync(() -> getUserDropDataList(query))
                .exceptionally(ex -> {
                    log.error("处理下拉框数据异常", ex);
                    return Collections.emptyList();
                });
        CompletableFuture<List<FillDayValueDTO>> fillValuesFuture = CompletableFuture.supplyAsync(() -> getFillValuesList(query))
                .exceptionally(ex -> {
                    log.error("处理已填报数据异常", ex);
                    return Collections.emptyList();
                });
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(weekTimeFuture, dropDataListFuture, fillValuesFuture);
        try {
            allFutures.join();
            fillDTO.setDayTime(weekTimeFuture.get());
            List<String> dropDataDTOS = dropDataListFuture.get();
            fillDTO.setDropDataList(dropDataDTOS);
            List<FillDayValueDTO> fillValueDTOS = fillValuesFuture.get();
//            fillValueDTOS = this.filterOldData(fillValueDTOS,dropDataDTOS);
            fillDTO.setFillValues(fillValueDTOS);
        } catch (Exception e) {
            log.error("异步任务执行失败", e);
            throw new BusinessException(MessageCode.QUERY_FILL_DATA_ERROR);
        }
        return fillDTO;
    }

    /**
     * 日填报内容
     *
     * @param command
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveDayFill(FillSaveDayCommand command) {
        TfsDayFillLogPO tfsDayFillLogPO = tfsDayFillLogMapper.selectByOpenIdAndDay(command.getOpenId(), command.getDay());
        if (ObjUtil.isNull(tfsDayFillLogPO)) {
            throw new BusinessException("未找到发送记录:command:" + command.getOpenId() + "day:" + JsonUtils.toJson(command.getDay()));
        }
        if (tfsDayFillLogPO.getStatus().equals(StatusEnum.PROJECT)) {
            tfsDayFillLogPO.setStatus(StatusEnum.DEMAND);
            tfsDayFillLogMapper.updateById(tfsDayFillLogPO);
        }
        tfsDayFillMapper.deleteByOpenIdAndDay(command.getOpenId(), command.getDay());
        for (FillDayValueDTO fillDayValueDTO : command.getFillValues()) {
            TfsDayFillPO po = new TfsDayFillPO()
                    .setFillDate(command.getDay())
                    .setOpenId(command.getOpenId())
                    .setName(tfsDayFillLogPO.getName())
                    .setTelephone(tfsDayFillLogPO.getTelephone())
                    .setProjectName(fillDayValueDTO.getProjectName())
                    .setWorkHour(fillDayValueDTO.getWorkHour());
            tfsDayFillMapper.insert(po);
        }
        return true;
    }


    private List<String> getUserDropDataList(DropDayQuery query) {

        String cacheKey = CacheConstants.getProjectList();
        String projectList = stringRedisTemplate.opsForValue().get(cacheKey);
        if (StrUtil.isNotEmpty(projectList)) {
            return JsonUtils.parseToList(projectList, String.class);
        } else {
            String oneWayWikToken = tfsProperties.getWik().getOneWayWikToken();
            String oneWayWikTableId = tfsProperties.getWik().getOneWayWikTableId();
            List<AppTableRecord> wikRecordList = feishuAppClient.getWikRecordList(oneWayWikToken, oneWayWikTableId, null);
            List<String> resultList = new ArrayList<>();
            for (AppTableRecord appTableRecord : wikRecordList) {
                Map<String, Object> recordMap = appTableRecord.getFields();
                Object row1 = recordMap.get("业务线");
                if (ObjUtil.isNull(row1)) {
                    continue;
                }
                resultList.add(FeishuAppHelper.getDrop(row1));
            }
            if (CollUtil.isNotEmpty(resultList)) {
                resultList = resultList.stream().distinct().collect(Collectors.toList());
                stringRedisTemplate.opsForValue().set(cacheKey, JsonUtils.toJson(resultList), 5, TimeUnit.MINUTES);
            }
            return resultList;
        }
    }

    private List<FillDayValueDTO> getFillValuesList(DropDayQuery query) {
        List<TfsDayFillPO> resultList = tfsDayFillMapper.selectByOpenIdAndDay(query.getOpenId(), query.getDay());
        List<FillDayValueDTO> collect = resultList.stream().map(po -> {
            FillDayValueDTO dto = new FillDayValueDTO();
            dto.setProjectName(po.getProjectName());
            dto.setWorkHour(po.getWorkHour());
            return dto;
        }).collect(Collectors.toList());
        return collect;
    }

}
