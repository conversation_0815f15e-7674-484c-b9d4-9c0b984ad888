package cn.genn.orch.tfs.application.job;

import cn.genn.job.xxl.component.AbstractJobHandler;
import cn.genn.orch.tfs.application.service.FsNotifyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;

/**
 * 定时发送飞书消息
 */
@Slf4j
@Component
public class NotifyTask extends AbstractJobHandler {

    @Resource
    private FsNotifyService fsNotifyService;

    @Override
    public void doExecute() {
        log.info("定时发送飞书消息 开始执行");
        long start = System.currentTimeMillis();

//        fsNotifyService.sendNotifyByWik(LocalDate.now());  //从多维表格获取用户推送通知
        fsNotifyService.sendNotifyByAll(LocalDate.now());    //应用范围内所有用户都推送通知
        log.info("定时发送飞书消息,执行耗时:{}s", (System.currentTimeMillis() - start) / 1000);
    }
}
