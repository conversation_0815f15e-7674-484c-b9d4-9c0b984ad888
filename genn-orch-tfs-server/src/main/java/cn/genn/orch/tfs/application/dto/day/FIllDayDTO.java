package cn.genn.orch.tfs.application.dto.day;

import cn.genn.orch.tfs.application.query.DropDayQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
public class FIllDayDTO {

    @ApiModelProperty("飞书用户OpenId")
    private String openId;

    @ApiModelProperty("填报日期")
    private LocalDate day;

    @ApiModelProperty("范围内总工时(h)")
    private Integer dayTime;

    @ApiModelProperty("已填报内容")
    private List<FillDayValueDTO> fillValues;

    @ApiModelProperty("填报下拉选项")
    private List<String> dropDataList;

    public FIllDayDTO(DropDayQuery query) {
        this.openId = query.getOpenId();
        this.day = query.getDay();
    }
}
