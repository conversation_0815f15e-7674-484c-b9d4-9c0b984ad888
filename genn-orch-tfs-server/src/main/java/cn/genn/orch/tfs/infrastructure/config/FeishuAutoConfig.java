package cn.genn.orch.tfs.infrastructure.config;

import cn.genn.core.exception.BusinessException;
import cn.genn.orch.tfs.infrastructure.properties.FeishuProperties;
import cn.genn.orch.tfs.infrastructure.properties.TfsProperties;
import com.lark.oapi.Client;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

@Configuration
public class FeishuAutoConfig {

    @Bean
    public Client feishuClient(TfsProperties tfsProperties) {
        FeishuProperties feishu = tfsProperties.getFeishu();
        if (feishu.isEnabled()) {
            return Client.newBuilder(feishu.getAppId(), feishu.getAppSecret())
                    .requestTimeout(3, TimeUnit.SECONDS) // 设置httpclient 超时时间，默认永不超时
                    .build();
        }
        throw new BusinessException("飞书配置异常!");
    }
}
