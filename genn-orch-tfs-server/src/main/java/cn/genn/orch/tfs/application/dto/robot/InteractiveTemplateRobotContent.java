package cn.genn.orch.tfs.application.dto.robot;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 消息卡片(使用模板方式)
 *
 * <AUTHOR>
 * @see <a href="https://open.larksuite.com/document/ukTMukTMukTM/uYzM3QjL2MzN04iNzcDN/message-card-builder">卡片搭建文档</a>
 * @see <a href="https://open.feishu.cn/cardkit">卡片搭建工具</a>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class InteractiveTemplateRobotContent extends RobotContent {

    private String type = "template";

    private CardTemplateData data;

}
