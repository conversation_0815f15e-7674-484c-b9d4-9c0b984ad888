package cn.genn.orch.tfs.application.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class FillDetail {

    @ApiModelProperty("行数据id,有这个id表示编辑,没有则新增")
    private String recordId;

    @ApiModelProperty("项目json")
    @NotBlank(message = "项目不能为空")
    private String projectJson;

    @ApiModelProperty("需求json")
    @NotBlank(message = "需求不能为空")
    private String demandJson;

    @ApiModelProperty("工时")
    @NotNull(message = "工时不能为空")
    private Integer workHour;

}
