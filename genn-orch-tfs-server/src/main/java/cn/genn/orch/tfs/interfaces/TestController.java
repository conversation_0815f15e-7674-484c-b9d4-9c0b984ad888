package cn.genn.orch.tfs.interfaces;

import cn.genn.orch.tfs.application.client.FeishuAppClient;
import cn.genn.orch.tfs.application.dto.UserNotifyDTO;
import cn.genn.orch.tfs.application.service.FsDayNotifyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;

@Slf4j
@RestController
@RequestMapping("/test")
public class TestController {

    @Resource
    private FeishuAppClient feishuAppClient;
    @Resource
    private FsDayNotifyService fsDayNotifyService;

    @GetMapping("/test")
    public String test() {
        UserNotifyDTO dto = new UserNotifyDTO();
        dto.setTelephones(Collections.singletonList("18435186300"));
        fsDayNotifyService.sendNotifyByTool(dto);

//        fsNotifyService.sendNotifyByAll(LocalDate.now());
        return null;
    }
}
