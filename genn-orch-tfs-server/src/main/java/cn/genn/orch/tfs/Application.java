package cn.genn.orch.tfs;

import cn.genn.orch.tfs.infrastructure.properties.TfsProperties;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.transaction.annotation.EnableTransactionManagement;


/**
 * <AUTHOR>
 */
@MapperScan({"cn.genn.orch.tfs.infrastructure.mapper"})
@SpringBootApplication
@EnableTransactionManagement
@EnableConfigurationProperties({TfsProperties.class})
public class Application {

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
