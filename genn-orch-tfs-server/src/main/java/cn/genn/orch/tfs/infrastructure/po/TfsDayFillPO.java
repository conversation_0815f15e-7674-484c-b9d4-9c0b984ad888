package cn.genn.orch.tfs.infrastructure.po;

import cn.genn.core.model.enums.DeletedEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * TfsDayFillPO对象
 *
 * <AUTHOR>
 * @desc 日报填报记录
 */
@Data
@Accessors(chain = true)
@TableName(value = "tfs_day_fill", autoResultMap = true)
public class TfsDayFillPO {

    /**
     * 
     */
    @TableId
    private Long id;

    /**
     * 日报日期
     */
    @TableField("fill_date")
    private LocalDate fillDate;

    /**
     * 飞书open_id
     */
    @TableField("open_id")
    private String openId;

    /**
     * 姓名
     */
    @TableField("`name`")
    private String name;

    /**
     * 手机号
     */
    @TableField("telephone")
    private String telephone;

    /**
     * 项目名称
     */
    @TableField("project_name")
    private String projectName;

    /**
     * 工时（h）
     */
    @TableField("work_hour")
    private Float workHour;

    /**
     * 备注
     */
    @TableField("`desc`")
    private String desc;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private Long createUserId;

    /**
     * 创建者名称
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 头像
     */
    @TableField("create_user_avatar")
    private String createUserAvatar;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long updateUserId;

    /**
     * 修改人名称
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

    /**
     * 逻辑删除（0：未删除  1：删除）
     */
    @TableField("deleted")
    private DeletedEnum deleted;

}

