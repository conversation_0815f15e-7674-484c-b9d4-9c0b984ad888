package cn.genn.orch.tfs.infrastructure.mapper;

import cn.genn.orch.tfs.infrastructure.po.TfsDayFillPO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface TfsDayFillMapper extends BaseMapper<TfsDayFillPO> {


    default void deleteByOpenIdAndDay(String openId, LocalDate day) {
        LambdaQueryWrapper<TfsDayFillPO> wrapper = Wrappers.lambdaQuery(TfsDayFillPO.class).eq(TfsDayFillPO::getOpenId, openId).eq(TfsDayFillPO::getFillDate, day);
        delete(wrapper);
    }

    default List<TfsDayFillPO> selectByOpenIdAndDay(String openId, LocalDate day) {
        LambdaQueryWrapper<TfsDayFillPO> wrapper = Wrappers.lambdaQuery(TfsDayFillPO.class).eq(TfsDayFillPO::getOpenId, openId).eq(TfsDayFillPO::getFillDate, day);
        return selectList(wrapper);
    }
}
