package cn.genn.orch.tfs.application.service;

import cn.genn.orch.tfs.application.client.FeishuAppClient;
import cn.genn.orch.tfs.application.dto.FsUser;
import cn.genn.orch.tfs.application.dto.UserNotifyDTO;
import cn.genn.orch.tfs.infrastructure.enums.StatusEnum;
import cn.genn.orch.tfs.infrastructure.mapper.TfsDayFillLogMapper;
import cn.genn.orch.tfs.infrastructure.po.TfsDayFillLogPO;
import cn.genn.orch.tfs.infrastructure.properties.TfsProperties;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.lark.oapi.service.contact.v3.model.User;
import com.lark.oapi.service.contact.v3.model.UserContactInfo;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FsDayNotifyService {

    @Resource
    private FeishuAppClient feishuAppClient;
    @Resource
    private TfsProperties tfsProperties;
    @Resource
    private TfsDayFillLogMapper tfsDayFillLogMapper;

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter DATE_TIME_FORMATTER2 = DateTimeFormatter.ofPattern("M月d日");

    /**
     * 指定人员通知
     *
     * @param userNotifyDTO
     */
    public void sendNotifyByTool(UserNotifyDTO userNotifyDTO) {
        LocalDate day = LocalDate.now();
        if (ObjUtil.isNotNull(userNotifyDTO.getFillTime())) {
            day = userNotifyDTO.getFillTime();
        }
        List<UserContactInfo> openIdByTelephone = feishuAppClient.getOpenIdByTelephone(userNotifyDTO.getTelephones());
        List<String> openIds = openIdByTelephone.stream().map(UserContactInfo::getUserId).distinct().collect(Collectors.toList());
        if(CollUtil.isEmpty(openIds)){
            return;
        }
        List<User> userBatch = feishuAppClient.getUserBatch(openIds);
        List<FsUser> users = userBatch.stream().map(userInfo -> {
            FsUser fsUser = new FsUser();
            fsUser.setOpenId(userInfo.getOpenId());
            fsUser.setName(userInfo.getName());
            fsUser.setTelephone(userInfo.getMobile());
            return fsUser;
        }).collect(Collectors.toList());
        this.sendNotify(users, day);
    }

    /**
     * 所有权限范围内的人员都发送通知
     */
    public void sendNotifyByAll(LocalDate day) {
        List<String> departmentAll = feishuAppClient.getDepartmentAll();
        List<User> departmentUserList = feishuAppClient.getDepartmentUserList(departmentAll);
        List<FsUser> users = departmentUserList.stream().map(userInfo -> {
            FsUser fsUser = new FsUser();
            fsUser.setOpenId(userInfo.getOpenId());
            fsUser.setName(userInfo.getName());
            fsUser.setTelephone(userInfo.getMobile());
            return fsUser;
        }).distinct().collect(Collectors.toList());
        this.sendNotify(users, day);
    }


    @SneakyThrows
    public void sendNotify(List<FsUser> users, LocalDate day) {
        if (CollUtil.isEmpty(users)) {
            return;
        }
        //2.获取人员一日工时
        users = users.stream().distinct().collect(Collectors.toList());
        String cardTemplateId = tfsProperties.getCardSend().getDayTemplateId();
        String pcUrl = tfsProperties.getCardSend().getDayPcUrl() + "?day=" + day.format(DATE_TIME_FORMATTER);
        String appUrl = tfsProperties.getCardSend().getDayAppUrl() + "?startTime=" + day.format(DATE_TIME_FORMATTER);
        String timeRange = day.format(DATE_TIME_FORMATTER2);
        Map<String, FsUser> fsUserMap = users.stream().collect(Collectors.toMap(FsUser::getOpenId, Function.identity()));
        List<List<FsUser>> partition = Lists.partition(users, 10);
        for (List<FsUser> fsUsers : partition) {
            Map<String, Integer> workHourMap = feishuAppClient.getOverTimeCache(fsUsers.stream().map(FsUser::getOpenId).collect(Collectors.toList()), day, day);
            //3.发送卡片通知
            for (String key : workHourMap.keySet()) {
                if (workHourMap.get(key) > 0) {
                    //写入数据库
                    TfsDayFillLogPO po = tfsDayFillLogMapper.selectByOpenIdAndDay(key, day);
                    if (ObjUtil.isNull(po)) {
                        po = new TfsDayFillLogPO()
                                .setOpenId(key)
                                .setName(fsUserMap.get(key).getName())
                                .setTelephone(fsUserMap.get(key).getTelephone())
                                .setSendDate(day)
                                .setStatus(StatusEnum.PROJECT);
                        tfsDayFillLogMapper.insert(po);
                    }
                    String openPcUrl = pcUrl + "&openId=" + key;
                    openPcUrl = tfsProperties.getCardSend().getPcBaseUrl() + URLEncoder.encode(openPcUrl, "UTF-8");
                    String openAppUrl = appUrl + "&openId=" + key;
                    //卡片通知;
                    this.sendNotifyCard(key, timeRange, openPcUrl, openAppUrl, cardTemplateId);
                }
            }
        }
    }


    /**
     * 通知卡片
     */
    public void sendNotifyCard(String openId, String timeRange, String pcUrl, String appUrl, String templateId) {
        Map<String, Object> templateVariable = new HashMap<>();
        templateVariable.put("timeRange", timeRange);
        templateVariable.put("pcUrl", pcUrl);
        templateVariable.put("appUrl", appUrl);
        feishuAppClient.sendCard(openId, templateId, templateVariable);
    }
}
