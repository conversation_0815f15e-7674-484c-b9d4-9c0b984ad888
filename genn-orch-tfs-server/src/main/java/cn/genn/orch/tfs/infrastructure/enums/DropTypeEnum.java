package cn.genn.orch.tfs.infrastructure.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum DropTypeEnum {

    PROJECT("project", "项目"),
    DEMAND("demand", "需求"),

    ;

    @EnumValue
    @JsonValue
    private final String code;

    private final String description;

    private static final Map<String, DropTypeEnum> VALUES = new HashMap<>();

    static {
        for (final DropTypeEnum item : DropTypeEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static DropTypeEnum of(String code) {
        return VALUES.get(code);
    }
}
