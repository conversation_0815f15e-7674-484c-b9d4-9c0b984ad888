package cn.genn.orch.tfs.infrastructure.mapper;

import cn.genn.orch.tfs.infrastructure.po.TfsDayFillLogPO;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */

public interface TfsDayFillLogMapper extends BaseMapper<TfsDayFillLogPO> {

    default TfsDayFillLogPO selectByOpenIdAndDay(String openId, LocalDate day){
        LambdaQueryWrapper<TfsDayFillLogPO> wrapper = Wrappers.lambdaQuery(TfsDayFillLogPO.class)
                .eq(TfsDayFillLogPO::getOpenId, openId)
                .eq(TfsDayFillLogPO::getSendDate, day)
                .last("limit 1");
        return selectOne(wrapper);
    }

    default List<TfsDayFillLogPO> selectInfo(LocalDate start, LocalDate end, String status){
        LambdaQueryWrapper<TfsDayFillLogPO> wrapper = Wrappers.lambdaQuery(TfsDayFillLogPO.class)
                .between(ObjUtil.isNotEmpty(start) && ObjUtil.isNotEmpty(end), TfsDayFillLogPO::getSendDate, start, end)
                .eq(ObjUtil.isNotEmpty(status), TfsDayFillLogPO::getStatus, status);
        return selectList(wrapper);
    }
}
