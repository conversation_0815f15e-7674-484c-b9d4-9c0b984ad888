package cn.genn.orch.tfs.application.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * TfsDayFillLogDTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
//@ContentRowHeight(10) // 设置行高
public class TfsDayFillLogDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "")
    @ExcelProperty(value = "id", index = 0) // 设置列名和顺序
    @ColumnWidth(20) // 设置列宽
    private Long id;

    @ApiModelProperty(value = "发送时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ExcelProperty(value = "填报日期", index = 1) // 设置列名和顺序
    @ColumnWidth(20) // 设置列宽
    private LocalDate sendDate;

    @ApiModelProperty(value = "飞书open_id")
    @ExcelProperty(value = "openId", index = 2) // 设置列名和顺序
    @ColumnWidth(20) // 设置列宽
    private String openId;

    @ApiModelProperty(value = "名称")
    @ExcelProperty(value = "名字", index = 3) // 设置列名和顺序
    @ColumnWidth(20) // 设置列宽
    private String name;

    @ApiModelProperty(value = "手机号")
    @ExcelProperty(value = "手机号", index = 4) // 设置列名和顺序
    @ColumnWidth(20) // 设置列宽
    private String telephone;

    @ExcelProperty(value = "填报状态", index = 5) // 设置列名和顺序
    @ColumnWidth(20) // 设置列宽
    private String statusName;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ExcelProperty(value = "发送时间", index = 6) // 设置列名和顺序
    @ColumnWidth(30) // 设置列宽
    private LocalDateTime createTime;


}

