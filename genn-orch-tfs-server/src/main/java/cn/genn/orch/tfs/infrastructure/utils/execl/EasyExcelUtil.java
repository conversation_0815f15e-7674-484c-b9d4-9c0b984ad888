package cn.genn.orch.tfs.infrastructure.utils.execl;

import cn.genn.core.exception.BusinessException;
import cn.genn.orch.tfs.infrastructure.exception.MessageCode;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;

/**
 * EasyExcel简易封装,支持多个sheet页导出
 * 不支持大数据量分段导出
 */
@Slf4j
public class EasyExcelUtil {

    /**
     * 导出excel,单sheet
     */
    public static void exportExcel(HttpServletResponse response, String title, ExportExcelModel<?> exportExcelModel) {
        exportExcels(response, title, Collections.singletonList(exportExcelModel));
    }


    /**
     * 导出excel,多sheet
     *
     * @param response 响应
     * @param dataList 多个sheet数据
     */
    public static void exportExcels(HttpServletResponse response, String title, java.util.List<ExportExcelModel<?>> dataList) {
        java.io.BufferedOutputStream bufferedOutputStream = null;
        if (org.springframework.util.CollectionUtils.isEmpty(dataList)) {
            return;
        }
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");
            String fileName = java.net.URLEncoder.encode(title + java.time.LocalDate.now(), "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            bufferedOutputStream = new java.io.BufferedOutputStream(response.getOutputStream());

            ExcelWriter excelWriter = EasyExcel.write(bufferedOutputStream).build();
            for (int i = 0; i < dataList.size(); i++) {
                ExportExcelModel<?> data = dataList.get(i);
                WriteSheet writeSheet = EasyExcel.writerSheet(i, data.getSheetName()).head(data.getClazz()).build();
                // 这里如果使用高版本jdk会报错,请使用jdk8或者使用其他解决方式.
                excelWriter.write(data.getData(), writeSheet);
            }
            excelWriter.finish();
        } catch (java.io.IOException e) {
            log.error("导出失败,文件title:{}", title, e);
            throw new BusinessException(MessageCode.DOWNLOAD_FILE_ERROR);
        } finally {
            try {
                if (bufferedOutputStream != null) {
                    bufferedOutputStream.close();
                }
            } catch (java.io.IOException e) {
                log.error("关闭流失败",e);
            }
        }

    }

    /**
     * 根据文件扩展名获取 Excel 类型
     */
    public static ExcelTypeEnum getExcelType(MultipartFile file) {
        String fileName = file.getOriginalFilename();
        if (fileName != null && fileName.toLowerCase().endsWith(".xls")) {
            return ExcelTypeEnum.XLS;
        } else if (fileName != null && fileName.toLowerCase().endsWith(".xlsx")) {
            return ExcelTypeEnum.XLSX;
        } else {
            throw new IllegalArgumentException("不支持的文件格式");
        }
    }
}
