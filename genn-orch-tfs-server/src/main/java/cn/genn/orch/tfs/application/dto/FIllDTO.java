package cn.genn.orch.tfs.application.dto;

import cn.genn.orch.tfs.application.query.DropDataQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

/**
 * 填报内容
 */
@Data
public class FIllDTO {

    @ApiModelProperty("飞书用户OpenId")
    private String openId;

    @ApiModelProperty("填报时间范围-开始时间")
    private LocalDate startTime;

    @ApiModelProperty("填报时间范围-结束时间")
    private LocalDate endTime;

    @ApiModelProperty("范围内总工时(h)")
    private Integer weekTime;

    @ApiModelProperty("已填报内容")
    private List<FillValueDTO> fillValues;

    @ApiModelProperty("填报下拉选项")
    private List<DropDataDTO> dropDataList;

    public FIllDTO(DropDataQuery query) {
        this.openId = query.getOpenId();
        this.startTime = query.getStartTime();
        this.endTime = query.getEndTime();
    }
}
