package cn.genn.orch.tfs.infrastructure.execl;

import cn.genn.orch.tfs.interfaces.ExeclTestController;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;

import java.util.ArrayList;
import java.util.List;

/**
 * 解析以及,分段保存到数据库也可以在这段操作
 */
public class TemplateDateListener extends AnalysisEventListener<ExeclTestController.ExportTemplateCommand> {

    // 用于存储解析到的数据
    private final List<ExeclTestController.ExportTemplateCommand> dataList = new ArrayList<>();

    /**
     * 每行解析都会执行
     * @param analysisContext
     */
    @Override
    public void invoke(ExeclTestController.ExportTemplateCommand templateDTO, AnalysisContext analysisContext) {
        // 处理当前行数据
        System.out.println("解析到一条数据: " + templateDTO);
        // 将数据添加到列表中
        dataList.add(templateDTO);
    }

    /**
     * 解析完成最后执行
     * @param analysisContext
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        System.out.println("解析完成，共解析到 " + dataList.size() + " 条数据");
    }

    /**
     * 获取解析到的数据列表
     * @return 数据列表
     */
    public List<ExeclTestController.ExportTemplateCommand> getDataList() {
        return dataList;
    }
}
